/**
 * Domain Service Types and Interfaces
 */

import { ApiResponse } from '../api/types';

// Common query parameters
export interface BaseQueryParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
}

// User-related types
export interface User {
  id: string;
  email: string;
  name: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  roles: Role[];
  permissions: Permission[];
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  isSystem: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserDto {
  email: string;
  name: string;
  firstName?: string;
  lastName?: string;
  password: string;
  roleIds?: string[];
  isActive?: boolean;
}

export interface UpdateUserDto {
  name?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  roleIds?: string[];
  isActive?: boolean;
}

export interface UserQueryParams extends BaseQueryParams {
  role?: string;
  isActive?: boolean;
  createdAfter?: string;
  createdBefore?: string;
}

// Training-related types
export interface Training {
  id: string;
  title: string;
  description: string;
  content?: string;
  category: TrainingCategory;
  tags: string[];
  duration: number; // in minutes
  difficulty: TrainingDifficulty;
  instructor: User;
  participants: User[];
  maxParticipants?: number;
  status: TrainingStatus;
  startDate?: Date;
  endDate?: Date;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TrainingCategory {
  id: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  parentId?: string;
  children?: TrainingCategory[];
  createdAt: Date;
  updatedAt: Date;
}

export enum TrainingDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

export enum TrainingStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ARCHIVED = 'archived'
}

export interface CreateTrainingDto {
  title: string;
  description: string;
  content?: string;
  categoryId: string;
  tags?: string[];
  duration: number;
  difficulty: TrainingDifficulty;
  instructorId: string;
  maxParticipants?: number;
  startDate?: Date;
  endDate?: Date;
  isPublic?: boolean;
}

export interface UpdateTrainingDto {
  title?: string;
  description?: string;
  content?: string;
  categoryId?: string;
  tags?: string[];
  duration?: number;
  difficulty?: TrainingDifficulty;
  instructorId?: string;
  maxParticipants?: number;
  startDate?: Date;
  endDate?: Date;
  isPublic?: boolean;
  status?: TrainingStatus;
}

export interface TrainingQueryParams extends BaseQueryParams {
  categoryId?: string;
  instructorId?: string;
  difficulty?: TrainingDifficulty;
  status?: TrainingStatus;
  isPublic?: boolean;
  startAfter?: string;
  startBefore?: string;
  tags?: string[];
}

// Vendor-related types
export interface Vendor {
  id: string;
  name: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: Address;
  contactPerson?: ContactPerson;
  services: VendorService[];
  rating?: number;
  isActive: boolean;
  isVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

export interface ContactPerson {
  name: string;
  email: string;
  phone?: string;
  position?: string;
}

export interface VendorService {
  id: string;
  name: string;
  description?: string;
  category: string;
  price?: number;
  currency?: string;
  isActive: boolean;
}

export interface CreateVendorDto {
  name: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: Address;
  contactPerson?: ContactPerson;
  services?: Omit<VendorService, 'id'>[];
  isActive?: boolean;
}

export interface UpdateVendorDto {
  name?: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: Address;
  contactPerson?: ContactPerson;
  services?: VendorService[];
  rating?: number;
  isActive?: boolean;
  isVerified?: boolean;
}

export interface VendorQueryParams extends BaseQueryParams {
  category?: string;
  isActive?: boolean;
  isVerified?: boolean;
  minRating?: number;
  city?: string;
  country?: string;
}

// Email-related types
export interface Email {
  id: string;
  from: string;
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  body: string;
  isHtml: boolean;
  attachments?: EmailAttachment[];
  status: EmailStatus;
  sentAt?: Date;
  deliveredAt?: Date;
  openedAt?: Date;
  clickedAt?: Date;
  errorMessage?: string;
  templateId?: string;
  templateData?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface EmailAttachment {
  id: string;
  filename: string;
  contentType: string;
  size: number;
  url?: string;
  data?: string; // base64 encoded
}

export enum EmailStatus {
  DRAFT = 'draft',
  QUEUED = 'queued',
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  CLICKED = 'clicked',
  BOUNCED = 'bounced',
  FAILED = 'failed'
}

export interface SendEmailDto {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  body: string;
  isHtml?: boolean;
  attachments?: Omit<EmailAttachment, 'id'>[];
  templateId?: string;
  templateData?: Record<string, any>;
  scheduledAt?: Date;
}

export interface EmailQueryParams extends BaseQueryParams {
  status?: EmailStatus;
  from?: string;
  to?: string;
  sentAfter?: string;
  sentBefore?: string;
  templateId?: string;
}

// Wins-related types
export interface Win {
  id: string;
  title: string;
  description: string;
  category: WinCategory;
  impact: WinImpact;
  author: User;
  participants?: User[];
  tags: string[];
  metrics?: WinMetric[];
  attachments?: WinAttachment[];
  isPublic: boolean;
  isFeatured: boolean;
  likes: number;
  views: number;
  achievedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum WinCategory {
  PERSONAL = 'personal',
  TEAM = 'team',
  PROJECT = 'project',
  PROCESS = 'process',
  INNOVATION = 'innovation',
  CUSTOMER = 'customer',
  FINANCIAL = 'financial',
  OTHER = 'other'
}

export enum WinImpact {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface WinMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  description?: string;
}

export interface WinAttachment {
  id: string;
  filename: string;
  contentType: string;
  size: number;
  url: string;
}

export interface CreateWinDto {
  title: string;
  description: string;
  category: WinCategory;
  impact: WinImpact;
  participantIds?: string[];
  tags?: string[];
  metrics?: Omit<WinMetric, 'id'>[];
  attachments?: Omit<WinAttachment, 'id'>[];
  isPublic?: boolean;
  achievedAt?: Date;
}

export interface UpdateWinDto {
  title?: string;
  description?: string;
  category?: WinCategory;
  impact?: WinImpact;
  participantIds?: string[];
  tags?: string[];
  metrics?: WinMetric[];
  attachments?: WinAttachment[];
  isPublic?: boolean;
  isFeatured?: boolean;
  achievedAt?: Date;
}

export interface WinQueryParams extends BaseQueryParams {
  category?: WinCategory;
  impact?: WinImpact;
  authorId?: string;
  participantId?: string;
  isPublic?: boolean;
  isFeatured?: boolean;
  achievedAfter?: string;
  achievedBefore?: string;
  tags?: string[];
}

// Service interfaces
export interface UsersService {
  getUsers(params?: UserQueryParams): Promise<ApiResponse<User[]>>;
  getUser(id: string): Promise<ApiResponse<User>>;
  createUser(user: CreateUserDto): Promise<ApiResponse<User>>;
  updateUser(id: string, user: UpdateUserDto): Promise<ApiResponse<User>>;
  deleteUser(id: string): Promise<ApiResponse<void>>;
  getUserRoles(id: string): Promise<ApiResponse<Role[]>>;
  updateUserRoles(id: string, roleIds: string[]): Promise<ApiResponse<User>>;
  getUserPermissions(id: string): Promise<ApiResponse<Permission[]>>;
  searchUsers(query: string, limit?: number): Promise<ApiResponse<User[]>>;
  getUsersByRole(roleId: string): Promise<ApiResponse<User[]>>;
  getActiveUsers(): Promise<ApiResponse<User[]>>;
  deactivateUser(id: string): Promise<ApiResponse<User>>;
  activateUser(id: string): Promise<ApiResponse<User>>;
}

export interface TrainingService {
  getTrainings(params?: TrainingQueryParams): Promise<ApiResponse<Training[]>>;
  getTraining(id: string): Promise<ApiResponse<Training>>;
  createTraining(training: CreateTrainingDto): Promise<ApiResponse<Training>>;
  updateTraining(id: string, training: UpdateTrainingDto): Promise<ApiResponse<Training>>;
  deleteTraining(id: string): Promise<ApiResponse<void>>;
  enrollInTraining(trainingId: string, userId: string): Promise<ApiResponse<void>>;
  unenrollFromTraining(trainingId: string, userId: string): Promise<ApiResponse<void>>;
  getTrainingParticipants(id: string): Promise<ApiResponse<User[]>>;
  getTrainingCategories(): Promise<ApiResponse<TrainingCategory[]>>;
}

export interface VendorsService {
  getVendors(params?: VendorQueryParams): Promise<ApiResponse<Vendor[]>>;
  getVendor(id: string): Promise<ApiResponse<Vendor>>;
  createVendor(vendor: CreateVendorDto): Promise<ApiResponse<Vendor>>;
  updateVendor(id: string, vendor: UpdateVendorDto): Promise<ApiResponse<Vendor>>;
  deleteVendor(id: string): Promise<ApiResponse<void>>;
  rateVendor(id: string, rating: number): Promise<ApiResponse<Vendor>>;
  verifyVendor(id: string): Promise<ApiResponse<Vendor>>;
}

export interface EmailService {
  getEmails(params?: EmailQueryParams): Promise<ApiResponse<Email[]>>;
  getEmail(id: string): Promise<ApiResponse<Email>>;
  sendEmail(email: SendEmailDto): Promise<ApiResponse<Email>>;
  scheduleEmail(email: SendEmailDto): Promise<ApiResponse<Email>>;
  cancelScheduledEmail(id: string): Promise<ApiResponse<void>>;
  getEmailTemplates(): Promise<ApiResponse<any[]>>;
  getEmailStats(id: string): Promise<ApiResponse<any>>;
}

export interface WinsService {
  getWins(params?: WinQueryParams): Promise<ApiResponse<Win[]>>;
  getWin(id: string): Promise<ApiResponse<Win>>;
  createWin(win: CreateWinDto): Promise<ApiResponse<Win>>;
  updateWin(id: string, win: UpdateWinDto): Promise<ApiResponse<Win>>;
  deleteWin(id: string): Promise<ApiResponse<void>>;
  likeWin(id: string): Promise<ApiResponse<Win>>;
  unlikeWin(id: string): Promise<ApiResponse<Win>>;
  featureWin(id: string): Promise<ApiResponse<Win>>;
  unfeatureWin(id: string): Promise<ApiResponse<Win>>;
}