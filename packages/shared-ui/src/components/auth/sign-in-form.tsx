import React, { useState } from 'react';
import { cn } from '../../lib/utils';
import { LuminarButton } from '../../components/ui/actions/button-advanced';
import { LuminarInput } from '../../components/ui/forms/input';
import { LuminarLabel } from '../../components/ui/forms/label';
import { LuminarCheckbox } from '../../components/ui/forms/checkbox';
import { toast } from '../../lib/toast';
import { Eye, EyeOff, Mail, Lock } from 'lucide-react';
import { useAuth } from '../../providers/auth-provider';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant,
  defaultComponentProps
} from '../../types/component-props';

interface SignInFormProps {
  onSubmit?: (data: { email: string; password: string; rememberMe: boolean }) => void;
  onForgotPassword?: () => void;
  onSuccess?: () => void;
  loading?: boolean;
  className?: string;
  showRememberMe?: boolean;
  showForgotPassword?: boolean;
  useAuthProvider?: boolean;
}

export function SignInForm({
  onSubmit,
  onForgotPassword,
  onSuccess,
  loading = false,
  className,
  showRememberMe = true,
  showForgotPassword = true,
  useAuthProvider = true
}: SignInFormProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { login, isLoading: authLoading, error: authError } = useAuth();

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the errors above');
      return;
    }
    
    if (useAuthProvider) {
      setIsSubmitting(true);
      try {
        const success = await login({ email, password });
        if (success) {
          toast.success('Successfully signed in!');
          onSuccess?.();
        }
      } catch (error) {
        console.error('Login failed:', error);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      onSubmit?.({ email, password, rememberMe });
    }
  };

  const isLoading = loading || isSubmitting || authLoading;
  const displayError = authError || errors.email || errors.password;

  return (
    <form onSubmit={handleSubmit} className={cn("space-y-4", className)}>
      {authError && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{authError}</p>
        </div>
      )}
      
      <div className="space-y-2">
        <LuminarLabel htmlFor="email">Email</LuminarLabel>
        <LuminarInput
          id="email"
          type="email"
          placeholder="Enter your email"
          value={email}
          onChange={(value: string) => setEmail(value)}
          icon={Mail}
          iconPosition="left"
          error={errors.email}
          disabled={isLoading}
        />
        {errors.email && (
          <p className="text-sm text-destructive">{errors.email}</p>
        )}
      </div>

      <div className="space-y-2">
        <LuminarLabel htmlFor="password">Password</LuminarLabel>
        <div className="relative">
          <LuminarInput
            id="password"
            type={showPassword ? "text" : "password"}
            placeholder="Enter your password"
            value={password}
            onChange={(value: string) => setPassword(value)}
            icon={Lock}
            iconPosition="left"
            error={errors.password}
            disabled={isLoading}
            className="pr-12"
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-muted-foreground hover:text-foreground transition-colors z-10 flex items-center justify-center"
            disabled={isLoading}
          >
            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        </div>
        {errors.password && (
          <p className="text-sm text-destructive">{errors.password}</p>
        )}
      </div>

      {(showRememberMe || showForgotPassword) && (
        <div className="flex items-center justify-between">
          {showRememberMe && (
            <div className="flex items-center space-x-2">
              <LuminarCheckbox
                id="remember"
                checked={rememberMe}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setRememberMe(e.target.checked)}
                disabled={isLoading}
              />
              <LuminarLabel 
                htmlFor="remember" 
                className="text-sm font-normal cursor-pointer"
              >
                Remember me
              </LuminarLabel>
            </div>
          )}
          
          {showForgotPassword && (
            <button
              type="button"
              onClick={onForgotPassword}
              className="text-sm text-primary hover:underline"
              disabled={isLoading}
            >
              Forgot password?
            </button>
          )}
        </div>
      )}

      <LuminarButton 
        type="submit" 
        className="w-full" 
        loading={isLoading}
        disabled={isLoading}
      >
        {isLoading ? 'Signing in...' : 'Sign In'}
      </LuminarButton>
    </form>
  );
}