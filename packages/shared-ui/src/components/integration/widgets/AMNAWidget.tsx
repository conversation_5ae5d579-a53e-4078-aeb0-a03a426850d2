import React, { useState, useRef, useEffect } from 'react';
import { Box, IconButton, Badge, Popover, Paper, Typography, TextField, List, ListItem, ListItemText, Divider, Chip } from '@mui/material';
import { SmartToy, Send, Close } from '@mui/icons-material';
import { useIntegrationContext } from '../providers/IntegrationProvider';
import { useAMNAState } from '../../../hooks/useAMNAState';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant,
  defaultComponentProps
} from '../../../types/component-props';

export interface AMNAWidgetProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  initialOpen?: boolean;
  showBadge?: boolean;
  context?: any;
}

export const AMNAWidget: React.FC<AMNAWidgetProps> = ({
  position = 'bottom-right',
  initialOpen = false,
  showBadge = true,
  context
}) => {
  const [open, setOpen] = useState(initialOpen);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Array<{
    id: string;
    content: string;
    role: 'user' | 'assistant';
    timestamp: Date;
  }>>([]);
  
  const anchorRef = useRef<HTMLButtonElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const { connected, sendMessage } = useIntegrationContext();
  const amnaState = useAMNAState();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!message.trim() || !connected) return;

    const userMessage = {
      id: Date.now().toString(),
      content: message,
      role: 'user' as const,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage('');

    try {
      // Send to AMNA
      const response = await sendAMNAMessage(message, { context });
      
      const assistantMessage = {
        id: (Date.now() + 1).toString(),
        content: response.content,
        role: 'assistant' as const,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const getPositionStyles = () => {
    const base = { position: 'fixed' as const, zIndex: 1000 };
    
    switch (position) {
      case 'bottom-right':
        return { ...base, bottom: 20, right: 20 };
      case 'bottom-left':
        return { ...base, bottom: 20, left: 20 };
      case 'top-right':
        return { ...base, top: 20, right: 20 };
      case 'top-left':
        return { ...base, top: 20, left: 20 };
    }
  };

  return (
    <>
      <IconButton
        ref={anchorRef}
        onClick={() => setOpen(!open)}
        sx={{
          ...getPositionStyles(),
          bgcolor: 'primary.main',
          color: 'white',
          '&:hover': {
            bgcolor: 'primary.dark',
          },
          width: 56,
          height: 56,
        }}
      >
        <Badge
          badgeContent={amnaState.unreadCount}
          color="error"
          invisible={!showBadge || amnaState.unreadCount === 0}
        >
          <SmartToy />
        </Badge>
      </IconButton>

      <Popover
        open={open}
        anchorEl={anchorRef.current}
        onClose={() => setOpen(false)}
        anchorOrigin={{
          vertical: position.startsWith('bottom') ? 'top' : 'bottom',
          horizontal: position.endsWith('right') ? 'left' : 'right',
        }}
        transformOrigin={{
          vertical: position.startsWith('bottom') ? 'bottom' : 'top',
          horizontal: position.endsWith('right') ? 'right' : 'left',
        }}
        PaperProps={{
          sx: {
            width: 350,
            height: 500,
            display: 'flex',
            flexDirection: 'column',
          }
        }}
      >
        <Paper sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SmartToy color="primary" />
              <Typography variant="h6">AMNA Assistant</Typography>
            </Box>
            <IconButton size="small" onClick={() => setOpen(false)}>
              <Close />
            </IconButton>
          </Box>
          {connected ? (
            <Chip label="Connected" color="success" size="small" sx={{ mt: 1 }} />
          ) : (
            <Chip label="Offline" color="error" size="small" sx={{ mt: 1 }} />
          )}
        </Paper>

        <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
          {messages.length === 0 ? (
            <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 4 }}>
              Hi! I'm AMNA, your AI assistant. How can I help you today?
            </Typography>
          ) : (
            <List>
              {messages.map((msg, index) => (
                <ListItem
                  key={msg.id}
                  sx={{
                    flexDirection: 'column',
                    alignItems: msg.role === 'user' ? 'flex-end' : 'flex-start',
                  }}
                >
                  <Paper
                    sx={{
                      p: 1.5,
                      maxWidth: '80%',
                      bgcolor: msg.role === 'user' ? 'primary.main' : 'grey.100',
                      color: msg.role === 'user' ? 'white' : 'text.primary',
                    }}
                  >
                    <Typography variant="body2">{msg.content}</Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        display: 'block',
                        mt: 0.5,
                        opacity: 0.7,
                      }}
                    >
                      {msg.timestamp.toLocaleTimeString()}
                    </Typography>
                  </Paper>
                </ListItem>
              ))}
              <div ref={messagesEndRef} />
            </List>
          )}
        </Box>

        <Divider />

        <Box sx={{ p: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Type your message..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            disabled={!connected}
            InputProps={{
              endAdornment: (
                <IconButton
                  size="small"
                  onClick={handleSendMessage}
                  disabled={!connected || !message.trim()}
                >
                  <Send />
                </IconButton>
              ),
            }}
          />
        </Box>
      </Popover>
    </>
  );
};