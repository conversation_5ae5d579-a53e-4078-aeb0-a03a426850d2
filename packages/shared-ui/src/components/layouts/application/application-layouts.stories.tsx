import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import React from 'react';
import { SaaSAppShell } from './saas-app-shell';
import { EcommerceLayout } from './ecommerce-layout';
import { 
  Home, 
  Users, 
  Settings, 
  Bell, 
  Search, 
  ShoppingCart, 
  Package, 
  BarChart3,
  User,
  Menu,
  X
} from 'lucide-react';

const meta: Meta = {
  title: 'Layouts/Application',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Application layout components for different types of applications including SaaS platforms and e-commerce sites.'
      }
    }
  },
  argTypes: {
    theme: {
      control: 'select',
      options: ['light', 'dark'],
      description: 'Theme variant'
    },
    sidebarCollapsed: {
      control: 'boolean',
      description: 'Sidebar collapsed state'
    },
    showNotifications: {
      control: 'boolean',
      description: 'Show notification badge'
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data for the stories
const mockNavigation = [
  { id: 'dashboard', label: 'Dashboard', icon: Home, href: '/dashboard' },
  { id: 'users', label: 'Users', icon: Users, href: '/users' },
  { id: 'analytics', label: 'Analytics', icon: BarChart3, href: '/analytics' },
  { id: 'settings', label: 'Settings', icon: Settings, href: '/settings' },
];

const mockUser = {
  id: 'user-1',
  name: 'John Doe',
  email: '<EMAIL>',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face'
};

const mockProducts = [
  {
    id: '1',
    name: 'Wireless Headphones',
    price: 99.99,
    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=200&h=200&fit=crop',
    category: 'Electronics'
  },
  {
    id: '2',
    name: 'Smart Watch',
    price: 199.99,
    image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=200&h=200&fit=crop',
    category: 'Electronics'
  },
  {
    id: '3',
    name: 'Running Shoes',
    price: 79.99,
    image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200&h=200&fit=crop',
    category: 'Sports'
  }
];

const SampleContent: React.FC<{ title: string }> = ({ title }) => (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{title}</h1>
      <div className="flex items-center gap-2">
        <button className="p-2 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors">
          <Bell className="w-4 h-4" />
        </button>
        <button className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
          <Search className="w-4 h-4" />
        </button>
      </div>
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[1, 2, 3, 4, 5, 6].map((item) => (
        <div key={item} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900 dark:text-white">Card {item}</h3>
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
              <Package className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
            This is a sample card with some content to demonstrate the layout.
          </p>
          <div className="flex items-center justify-between">
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              {Math.floor(Math.random() * 1000)}
            </span>
            <span className="text-sm text-green-600 dark:text-green-400">
              +{Math.floor(Math.random() * 20)}%
            </span>
          </div>
        </div>
      ))}
    </div>
    
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <h3 className="font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h3>
      <div className="space-y-3">
        {[1, 2, 3, 4, 5].map((item) => (
          <div key={item} className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1">
              <p className="text-sm text-gray-900 dark:text-white">
                Activity item {item} - Something interesting happened
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {Math.floor(Math.random() * 60)} minutes ago
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export const SaasAppShellDemo: Story = {
  args: {
    sidebarCollapsed: false,
    showNotifications: true,
    theme: 'light'
  },
  render: (args) => (
    <SaasAppShell
      navigation={mockNavigation}
      user={mockUser}
      sidebarCollapsed={args.sidebarCollapsed}
      onSidebarToggle={() => {}}
      currentPath="/dashboard"
      showNotifications={args.showNotifications}
      notificationCount={5}
      onNotificationClick={() => {}}
      headerActions={
        <div className="flex items-center gap-2">
          <button className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
            <Search className="w-4 h-4" />
          </button>
          <button className="p-2 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors">
            <Bell className="w-4 h-4" />
          </button>
        </div>
      }
    >
      <SampleContent title="SaaS Dashboard" />
    </SaasAppShell>
  )
};

export const SaasAppShellCollapsed: Story = {
  args: {
    sidebarCollapsed: true,
    showNotifications: true,
    theme: 'light'
  },
  render: (args) => (
    <SaasAppShell
      navigation={mockNavigation}
      user={mockUser}
      sidebarCollapsed={args.sidebarCollapsed}
      onSidebarToggle={() => {}}
      currentPath="/users"
      showNotifications={args.showNotifications}
      notificationCount={12}
      onNotificationClick={() => {}}
      headerActions={
        <div className="flex items-center gap-2">
          <button className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
            <Search className="w-4 h-4" />
          </button>
          <button className="p-2 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors">
            <Bell className="w-4 h-4" />
          </button>
        </div>
      }
    >
      <SampleContent title="Users Management" />
    </SaasAppShell>
  )
};

export const EcommerceLayoutDemo: Story = {
  args: {
    theme: 'light',
    showNotifications: false
  },
  render: (args) => (
    <EcommerceLayout
      cartItemCount={3}
      user={mockUser}
      onCartClick={() => {}}
      onSearchSubmit={(query) => console.log('Search:', query)}
      categories={[
        { id: 'electronics', name: 'Electronics', href: '/category/electronics' },
        { id: 'clothing', name: 'Clothing', href: '/category/clothing' },
        { id: 'sports', name: 'Sports', href: '/category/sports' },
        { id: 'home', name: 'Home & Garden', href: '/category/home' }
      ]}
      headerActions={
        <div className="flex items-center gap-2">
          <button className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
            <Search className="w-4 h-4" />
          </button>
          <button className="relative p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
            <ShoppingCart className="w-4 h-4" />
            <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
              3
            </span>
          </button>
        </div>
      }
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Featured Products</h1>
          <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium">
            View All
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockProducts.map((product) => (
            <div key={product.id} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
              <div className="aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
                <img 
                  src={product.image} 
                  alt={product.name}
                  className="w-full h-full object-cover rounded-lg"
                />
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold text-gray-900 dark:text-white">{product.name}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{product.category}</p>
                <div className="flex items-center justify-between">
                  <span className="text-xl font-bold text-gray-900 dark:text-white">
                    ${product.price}
                  </span>
                  <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                    Add to Cart
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="font-semibold text-gray-900 dark:text-white mb-4">Why Choose Us?</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-3">
                <Package className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Fast Shipping</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">Free shipping on orders over $50</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3">
                <ShoppingCart className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Easy Returns</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">30-day return policy</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-3">
                <Bell className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">24/7 Support</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">Always here to help</p>
            </div>
          </div>
        </div>
      </div>
    </EcommerceLayout>
  )
};

export const ResponsiveShowcase: Story = {
  render: () => (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Responsive Layout Examples
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          These layouts automatically adapt to different screen sizes
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">SaaS Application</h3>
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <div className="scale-50 origin-top-left w-[200%] h-[400px] overflow-hidden">
              <SaasAppShell
                navigation={mockNavigation}
                user={mockUser}
                sidebarCollapsed={false}
                onSidebarToggle={() => {}}
                currentPath="/dashboard"
                showNotifications={true}
                notificationCount={3}
                onNotificationClick={() => {}}
              >
                <SampleContent title="Dashboard" />
              </SaasAppShell>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">E-commerce Store</h3>
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <div className="scale-50 origin-top-left w-[200%] h-[400px] overflow-hidden">
              <EcommerceLayout
                cartItemCount={5}
                user={mockUser}
                onCartClick={() => {}}
                onSearchSubmit={(query) => console.log('Search:', query)}
                categories={[
                  { id: 'electronics', name: 'Electronics', href: '/category/electronics' },
                  { id: 'clothing', name: 'Clothing', href: '/category/clothing' }
                ]}
              >
                <div className="grid grid-cols-2 gap-4 p-4">
                  {mockProducts.slice(0, 4).map((product) => (
                    <div key={product.id} className="bg-white rounded-lg p-4 shadow-sm border">
                      <div className="aspect-square bg-gray-100 rounded mb-2"></div>
                      <h3 className="font-medium text-sm">{product.name}</h3>
                      <p className="text-lg font-bold">${product.price}</p>
                    </div>
                  ))}
                </div>
              </EcommerceLayout>
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Responsive layouts that work across different screen sizes and devices.'
      }
    }
  }
};