import { motion, MotionProps } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes } from "react"
import {
  type GlassConfig,
  createGlassStyles,
} from '../../../lib/glass-utils'
import { animationPresets, transitions } from '../../../design-system'
import { microInteractions } from '../../../lib/micro-interactions'
import { performanceMonitor } from '../../../lib/performance-monitor'
import type {
  CardBaseProps,
  ExtendedHTMLProps,
  defaultComponentProps,
  sizeToSpacing
} from '../../../types/component-props';

/**
 * Card component props extending the standardized interface
 */
export interface LuminarCardProps extends 
  CardBaseProps,
  ExtendedHTMLProps<HTMLAttributes<HTMLDivElement>> {
  /**
   * Card content
   */
  children?: React.ReactNode;
  
  /**
   * Whether to enable performance monitoring
   */
  enablePerformanceMonitoring?: boolean;
  
  /**
   * Custom glass configuration (overrides standard glass props)
   */
  glassConfig?: GlassConfig;
}

/**
 * Standardized Card Component
 * 
 * Implements the complete standardized props interface for consistent API
 * across all Luminar UI components.
 */
const LuminarCard = forwardRef<HTMLDivElement, LuminarCardProps>(
  ({
    // Standard component props
    className,
    id,
    style,
    'data-testid': dataTestId,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    
    // Variant props
    variant = defaultComponentProps.variant,
    size = defaultComponentProps.size,
    
    // Glass props
    glass = defaultComponentProps.glass,
    glassIntensity = defaultComponentProps.glassIntensity,
    glassDepth,
    glassConfig = { element: 'card' },
    
    // Animation props
    animation = defaultComponentProps.animation,
    disableAnimation = defaultComponentProps.disableAnimation,
    animationDuration,
    animationDelay,
    motionProps,
    
    // Interactive props
    interactive = defaultComponentProps.interactive,
    hoverable = defaultComponentProps.hoverable,
    
    // Layout props
    spacing,
    padding,
    margin,
    gap,
    maxWidth,
    maxHeight,
    centered,
    
    // Card specific props
    elevation = 1,
    clickable = false,
    onClick,
    bordered = false,
    
    // Performance props
    enablePerformanceMonitoring = false,
    
    // Children and other props
    children,
    ...props
  }, ref) => {
    // Get size-based spacing values
    const sizeSpacing = sizeToSpacing[size]
    
    // Apply glass styles if enabled
    const glassClasses = (glass || variant === 'glass') ? createGlassStyles({ 
      ...glassConfig,
      element: 'card',
      profile: glassIntensity === 'subtle' ? 'soft' : 
               glassIntensity === 'intense' ? 'hard' : 'standard',
      interactive: interactive || hoverable || clickable
    }) : '';

    // Get animation configuration
    const animationProps = disableAnimation || animation === 'none' ? {} : {
      ...(animationPresets[animation] || animationPresets.slideUp),
      transition: {
        ...transitions.default,
        duration: animationDuration || transitions.default.duration,
        delay: animationDelay || 0
      }
    }

    // Interaction props for hover/tap effects
    const interactionProps = ((interactive || hoverable || clickable) && !disableAnimation) ? {
      ...microInteractions.glassHover,
      whileTap: (interactive || clickable) ? microInteractions.buttonPress.whileTap : undefined
    } : {}

    // Handle click events
    const handleClick = () => {
      if (clickable && onClick) {
        onClick()
      }
    }

    // Get elevation shadow classes
    const elevationClasses = {
      0: '',
      1: 'shadow-sm',
      2: 'shadow-md',
      3: 'shadow-lg',
      4: 'shadow-xl',
      5: 'shadow-2xl'
    }[elevation] || 'shadow-sm'

    // Calculate layout styles
    const layoutStyles = {
      ...(spacing && { padding: typeof spacing === 'number' ? `${spacing}px` : sizeSpacing }),
      ...(padding && { padding: typeof padding === 'number' ? `${padding}px` : sizeToSpacing[padding] }),
      ...(margin && { margin: typeof margin === 'number' ? `${margin}px` : sizeToSpacing[margin] }),
      ...(maxWidth && { maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth }),
      ...(maxHeight && { maxHeight: typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight }),
      ...style
    }

    const cardContent = (
      <motion.div
        ref={ref}
        id={id}
        className={cn(
          "relative overflow-hidden rounded-lg",
          "text-crisp", // Enhanced text clarity
          glassClasses,
          elevationClasses,
          bordered && "border border-border",
          (interactive || clickable) && "cursor-pointer",
          centered && "flex items-center justify-center",
          gap && `gap-${gap}`,
          "will-change-transform backface-hidden", // Performance optimization
          className
        )}
        style={layoutStyles}
        onClick={handleClick}
        data-testid={dataTestId}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
        {...animationProps}
        {...interactionProps}
        {...motionProps}
        transition={transitions.default as any}
        {...props}
      >
        <div className="relative z-10">{children}</div>

        {/* Optional shimmer effect for interactive cards */}
        {(interactive || clickable) && (
          <div className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300">
            <div className="glass-shimmer" />
          </div>
        )}
      </motion.div>
    )

    return enablePerformanceMonitoring
      ? performanceMonitor.measureRender('LuminarCard', () => cardContent)
      : cardContent
  }
)
LuminarCard.displayName = "LuminarCard"

export { LuminarCard }
