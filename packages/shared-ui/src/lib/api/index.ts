// Export API client
export { apiClient } from './client';
export type { 
  ApiError, 
  ApiResponse, 
  TokenPair, 
  User, 
  ErrorCategory, 
  CategorizedError, 
  RetryConfig, 
  RequestMetadata 
} from './client';

// Export all API endpoints
export { authApi } from './endpoints/auth.api';
export { trainingApi } from './endpoints/training.api';
export { vendorApi } from './endpoints/vendor.api';
export { winsApi } from './endpoints/wins.api';
export { usersApi } from './endpoints/users.api';
export { emailApi } from './endpoints/email.api';

// Export types from auth
export type {
  LoginDto,
  RegisterDto,
  LoginResponse,
  RegisterResponse,
  ChangePasswordDto as AuthChangePasswordDto,
  PasswordResetRequestDto,
  PasswordResetConfirmDto,
  VerifyEmailDto
} from './endpoints/auth.api';

// Export types from training
export type {
  Skill,
  SkillLevel,
  TrainingCourse,
  TrainingEnrollment,
  Assessment,
  AssessmentResponse,
  SkillRating,
  SkillGap,
  TrainingRecommendation,
  LearningPath,
  CreateTrainingCourseDto,
  UpdateTrainingCourseDto,
  TrainingFiltersDto,
  CreateEnrollmentDto,
  UpdateEnrollmentDto,
  TrainingMetrics,
  CourseAnalytics,
  UserProgress
} from './endpoints/training.api';

// Export types from vendor
export type {
  PhoneNumber,
  Vendor,
  CostItem,
  Proposal,
  Review,
  CreateVendorDto,
  UpdateVendorDto,
  VendorFilters,
  CreateProposalDto,
  UpdateProposalDto,
  ProposalFilters,
  CreateReviewDto,
  DashboardStats as VendorDashboardStats,
  VendorPerformance
} from './endpoints/vendor.api';

// Export types from wins
export type {
  Achievement,
  Recognition,
  CostInitiative,
  TrainingIdea,
  Milestone,
  ProgressUpdate,
  Attachment,
  WeeklySubmission,
  SubmissionMetrics,
  CreateSubmissionDto,
  UpdateSubmissionDto,
  SubmissionFilters,
  DashboardStats as WinsDashboardStats,
  SubmissionSummary,
  TeamProgress
} from './endpoints/wins.api';

// Export types from users
export type {
  UserProfile,
  UserActivity,
  UserSession,
  UserPermission,
  UserRole,
  Department,
  UserStats,
  CreateUserDto,
  UpdateUserDto,
  UpdateUserPreferencesDto,
  ChangePasswordDto as UserChangePasswordDto,
  ResetPasswordDto,
  CreateRoleDto,
  UpdateRoleDto,
  CreateDepartmentDto,
  UpdateDepartmentDto,
  UserFilters,
  BulkUserAction
} from './endpoints/users.api';

// Export types from email
export type {
  EmailTemplate,
  EmailRecipient,
  EmailCampaign,
  EmailLog,
  EmailSettings,
  CreateEmailTemplateDto,
  UpdateEmailTemplateDto,
  CreateEmailCampaignDto,
  UpdateEmailCampaignDto,
  SendEmailDto,
  BulkEmailDto,
  EmailFilters
} from './endpoints/email.api';

// Convenience export for all APIs
// TODO: Fix variable names that don't match the actual exports
// export const api = {
//   auth: authApi,
//   training: trainingApi,
//   vendor: vendorApi,
//   wins: winsApi,
//   users: usersApi,
//   email: emailApi,
// };