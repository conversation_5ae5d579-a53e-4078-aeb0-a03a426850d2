import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { emailApi } from '../../api';
import { queryKeys, cacheUtils } from '../client';
import { toast } from '../../toast';
import type {
  EmailTemplate,
  EmailCampaign,
  EmailLog,
  EmailRecipient,
  EmailSettings,
  CreateEmailTemplateDto,
  UpdateEmailTemplateDto,
  CreateEmailCampaignDto,
  UpdateEmailCampaignDto,
  SendEmailDto,
  BulkEmailDto,
  EmailFilters,
} from '../../api';

// Email Template Hooks
export function useEmailTemplates(filters?: EmailFilters) {
  return useQuery({
    queryKey: queryKeys.email.templates.list(filters),
    queryFn: () => emailApi.getTemplates(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes for templates
  });
}

export function useEmailTemplate(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.email.templates.detail(id),
    queryFn: () => emailApi.getTemplate(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useEmailTemplatePreview(id: string, variables?: any, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.email.templates.preview(id, variables),
    queryFn: () => emailApi.previewTemplate(id, variables),
    enabled: enabled && !!id,
    staleTime: 1 * 60 * 1000, // 1 minute for preview
  });
}

export function useCreateEmailTemplate() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateEmailTemplateDto) => emailApi.createTemplate(data),
    onSuccess: (response) => {
      // Invalidate template lists
      cacheUtils.invalidateQueries(queryKeys.email.templates.lists());
      
      // Add new template to cache
      queryClient.setQueryData(
        queryKeys.email.templates.detail(response.data.id),
        response
      );
      
      toast.success('Email template created successfully');
    },
  });
}

export function useUpdateEmailTemplate() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEmailTemplateDto }) => 
      emailApi.updateTemplate(id, data),
    onSuccess: (response, variables) => {
      // Update template in cache
      queryClient.setQueryData(
        queryKeys.email.templates.detail(variables.id),
        response
      );
      
      // Invalidate template lists
      cacheUtils.invalidateQueries(queryKeys.email.templates.lists());
      
      toast.success('Email template updated successfully');
    },
  });
}

export function useDeleteEmailTemplate() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => emailApi.deleteTemplate(id),
    onSuccess: (_, id) => {
      // Remove template from cache
      queryClient.removeQueries({ queryKey: queryKeys.email.templates.detail(id) });
      
      // Invalidate template lists
      cacheUtils.invalidateQueries(queryKeys.email.templates.lists());
      
      toast.success('Email template deleted successfully');
    },
  });
}

export function useTestEmailTemplate() {
  return useMutation({
    mutationFn: ({ id, email, variables }: { id: string; email: string; variables?: any }) => 
      emailApi.testTemplate(id, email, variables),
    onSuccess: () => {
      toast.success('Test email sent successfully');
    },
  });
}

// Email Campaign Hooks
export function useEmailCampaigns(filters?: EmailFilters) {
  return useQuery({
    queryKey: queryKeys.email.campaigns.list(filters),
    queryFn: () => emailApi.getCampaigns(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes for campaigns
  });
}

export function useEmailCampaign(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.email.campaigns.detail(id),
    queryFn: () => emailApi.getCampaign(id),
    enabled: enabled && !!id,
    staleTime: 2 * 60 * 1000,
  });
}

export function useEmailCampaignMetrics(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.email.campaigns.metrics(id),
    queryFn: () => emailApi.getCampaignMetrics(id),
    enabled: enabled && !!id,
    staleTime: 1 * 60 * 1000, // 1 minute for metrics
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for active campaigns
  });
}

export function useCreateEmailCampaign() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateEmailCampaignDto) => emailApi.createCampaign(data),
    onSuccess: (response) => {
      // Invalidate campaign lists
      cacheUtils.invalidateQueries(queryKeys.email.campaigns.lists());
      
      // Add new campaign to cache
      queryClient.setQueryData(
        queryKeys.email.campaigns.detail(response.data.id),
        response
      );
      
      toast.success('Email campaign created successfully');
    },
  });
}

export function useUpdateEmailCampaign() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEmailCampaignDto }) => 
      emailApi.updateCampaign(id, data),
    onSuccess: (response, variables) => {
      // Update campaign in cache
      queryClient.setQueryData(
        queryKeys.email.campaigns.detail(variables.id),
        response
      );
      
      // Invalidate campaign lists
      cacheUtils.invalidateQueries(queryKeys.email.campaigns.lists());
      
      toast.success('Email campaign updated successfully');
    },
  });
}

export function useDeleteEmailCampaign() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => emailApi.deleteCampaign(id),
    onSuccess: (_, id) => {
      // Remove campaign from cache
      queryClient.removeQueries({ queryKey: queryKeys.email.campaigns.detail(id) });
      
      // Invalidate campaign lists
      cacheUtils.invalidateQueries(queryKeys.email.campaigns.lists());
      
      toast.success('Email campaign deleted successfully');
    },
  });
}

export function useSendEmailCampaign() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => emailApi.sendCampaign(id),
    onSuccess: (_, id) => {
      // Invalidate campaign to refetch updated status
      cacheUtils.invalidateQueries(queryKeys.email.campaigns.detail(id));
      
      // Invalidate campaign lists
      cacheUtils.invalidateQueries(queryKeys.email.campaigns.lists());
      
      toast.success('Email campaign sent successfully');
    },
  });
}

export function useScheduleEmailCampaign() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, scheduledAt }: { id: string; scheduledAt: string }) => 
      emailApi.scheduleCampaign(id, scheduledAt),
    onSuccess: (response, variables) => {
      // Update campaign in cache
      queryClient.setQueryData(
        queryKeys.email.campaigns.detail(variables.id),
        response
      );
      
      // Invalidate campaign lists
      cacheUtils.invalidateQueries(queryKeys.email.campaigns.lists());
      
      toast.success('Email campaign scheduled successfully');
    },
  });
}

export function usePauseEmailCampaign() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => emailApi.pauseCampaign(id),
    onSuccess: (response, id) => {
      // Update campaign in cache
      queryClient.setQueryData(
        queryKeys.email.campaigns.detail(id),
        response
      );
      
      // Invalidate campaign lists
      cacheUtils.invalidateQueries(queryKeys.email.campaigns.lists());
      
      toast.success('Email campaign paused successfully');
    },
  });
}

export function useResumeEmailCampaign() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => emailApi.resumeCampaign(id),
    onSuccess: (response, id) => {
      // Update campaign in cache
      queryClient.setQueryData(
        queryKeys.email.campaigns.detail(id),
        response
      );
      
      // Invalidate campaign lists
      cacheUtils.invalidateQueries(queryKeys.email.campaigns.lists());
      
      toast.success('Email campaign resumed successfully');
    },
  });
}

// Email Sending Hooks
export function useSendEmail() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: SendEmailDto) => emailApi.sendEmail(data),
    onSuccess: () => {
      // Invalidate email logs
      cacheUtils.invalidateQueries(queryKeys.email.logs.lists());
      
      toast.success('Email sent successfully');
    },
  });
}

export function useSendBulkEmail() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: BulkEmailDto) => emailApi.sendBulkEmail(data),
    onSuccess: (response) => {
      // Invalidate email logs
      cacheUtils.invalidateQueries(queryKeys.email.logs.lists());
      
      // Invalidate queue status
      cacheUtils.invalidateQueries(queryKeys.email.queue());
      
      toast.success(`${response.data.queued} emails queued for sending`);
    },
  });
}

export function useSendTransactionalEmail() {
  return useMutation({
    mutationFn: (data: {
      to: string;
      subject: string;
      body: string;
      isHtml?: boolean;
      priority?: 'low' | 'normal' | 'high';
    }) => emailApi.sendTransactionalEmail(data),
    onSuccess: () => {
      toast.success('Email sent successfully');
    },
  });
}

// Email Logs Hooks
export function useEmailLogs(filters?: EmailFilters) {
  return useQuery({
    queryKey: queryKeys.email.logs.list(filters),
    queryFn: () => emailApi.getEmailLogs(filters),
    staleTime: 30 * 1000, // 30 seconds for logs
  });
}

export function useEmailLog(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.email.logs.detail(id),
    queryFn: () => emailApi.getEmailLog(id),
    enabled: enabled && !!id,
    staleTime: 30 * 1000,
  });
}

export function useRetryFailedEmail() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => emailApi.retryFailedEmail(id),
    onSuccess: (response, id) => {
      // Update email log in cache
      queryClient.setQueryData(
        queryKeys.email.logs.detail(id),
        response
      );
      
      // Invalidate email logs
      cacheUtils.invalidateQueries(queryKeys.email.logs.lists());
      
      toast.success('Email retry initiated');
    },
  });
}

// Email Recipients Hooks
export function useEmailRecipients(filters?: EmailFilters) {
  return useQuery({
    queryKey: queryKeys.email.recipients.list(filters),
    queryFn: () => emailApi.getRecipients(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes for recipients
  });
}

export function useAddEmailRecipient() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: {
      email: string;
      name: string;
      departmentId?: string;
      roleId?: string;
    }) => emailApi.addRecipient(data),
    onSuccess: () => {
      // Invalidate recipient lists
      cacheUtils.invalidateQueries(queryKeys.email.recipients.lists());
      
      toast.success('Email recipient added successfully');
    },
  });
}

export function useUpdateEmailRecipient() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { 
      id: string; 
      data: {
        email?: string;
        name?: string;
        departmentId?: string;
        roleId?: string;
        isActive?: boolean;
      };
    }) => emailApi.updateRecipient(id, data),
    onSuccess: () => {
      // Invalidate recipient lists
      cacheUtils.invalidateQueries(queryKeys.email.recipients.lists());
      
      toast.success('Email recipient updated successfully');
    },
  });
}

export function useDeleteEmailRecipient() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => emailApi.deleteRecipient(id),
    onSuccess: () => {
      // Invalidate recipient lists
      cacheUtils.invalidateQueries(queryKeys.email.recipients.lists());
      
      toast.success('Email recipient deleted successfully');
    },
  });
}

export function useImportEmailRecipients() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (file: File) => emailApi.importRecipients(file),
    onSuccess: (response) => {
      // Invalidate recipient lists
      cacheUtils.invalidateQueries(queryKeys.email.recipients.lists());
      
      toast.success(`${response.data.imported} recipients imported successfully`);
      
      if (response.data.failed > 0) {
        toast.error(`${response.data.failed} recipients failed to import`);
      }
    },
  });
}

// Email Settings Hooks
export function useEmailSettings() {
  return useQuery({
    queryKey: queryKeys.email.settings(),
    queryFn: () => emailApi.getSettings(),
    staleTime: 10 * 60 * 1000, // 10 minutes for settings
  });
}

export function useUpdateEmailSettings() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Partial<EmailSettings>) => emailApi.updateSettings(data),
    onSuccess: (response) => {
      // Update settings in cache
      queryClient.setQueryData(queryKeys.email.settings(), response);
      
      toast.success('Email settings updated successfully');
    },
  });
}

export function useTestEmailSettings() {
  return useMutation({
    mutationFn: () => emailApi.testSettings(),
    onSuccess: (response) => {
      if (response.data.success) {
        toast.success('Email settings test successful');
      } else {
        toast.error(`Email settings test failed: ${response.data.message}`);
      }
    },
  });
}

// Email Analytics Hooks
export function useEmailAnalytics(filters?: {
  dateFrom?: string;
  dateTo?: string;
  templateId?: string;
  campaignId?: string;
}) {
  return useQuery({
    queryKey: queryKeys.email.analytics(filters),
    queryFn: () => emailApi.getAnalytics(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes for analytics
  });
}

// Email Queue Hooks
export function useEmailQueueStatus() {
  return useQuery({
    queryKey: queryKeys.email.queue(),
    queryFn: () => emailApi.getQueueStatus(),
    staleTime: 30 * 1000, // 30 seconds for queue status
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  });
}

export function useClearEmailQueue() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => emailApi.clearQueue(),
    onSuccess: () => {
      // Invalidate queue status
      cacheUtils.invalidateQueries(queryKeys.email.queue());
      
      toast.success('Email queue cleared successfully');
    },
  });
}

export function useRetryFailedEmailJobs() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => emailApi.retryFailedJobs(),
    onSuccess: (response) => {
      // Invalidate queue status
      cacheUtils.invalidateQueries(queryKeys.email.queue());
      
      toast.success(`${response.data.retriedCount} jobs retried successfully`);
    },
  });
}

// Utility hooks for optimistic updates
export function useOptimisticEmailTemplateUpdate() {
  const queryClient = useQueryClient();
  
  return (templateId: string, updateFn: (template: EmailTemplate) => EmailTemplate) => {
    const previousTemplate = queryClient.getQueryData<{ data: EmailTemplate }>(
      queryKeys.email.templates.detail(templateId)
    );
    
    if (previousTemplate) {
      queryClient.setQueryData(
        queryKeys.email.templates.detail(templateId),
        {
          ...previousTemplate,
          data: updateFn(previousTemplate.data),
        }
      );
    }
    
    return previousTemplate;
  };
}

export function useOptimisticEmailCampaignUpdate() {
  const queryClient = useQueryClient();
  
  return (campaignId: string, updateFn: (campaign: EmailCampaign) => EmailCampaign) => {
    const previousCampaign = queryClient.getQueryData<{ data: EmailCampaign }>(
      queryKeys.email.campaigns.detail(campaignId)
    );
    
    if (previousCampaign) {
      queryClient.setQueryData(
        queryKeys.email.campaigns.detail(campaignId),
        {
          ...previousCampaign,
          data: updateFn(previousCampaign.data),
        }
      );
    }
    
    return previousCampaign;
  };
}

// Real-time hooks for email status updates
export function useEmailCampaignStatusPolling(campaignId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.email.campaigns.detail(campaignId),
    queryFn: () => emailApi.getCampaign(campaignId),
    enabled: enabled && !!campaignId,
    refetchInterval: (query) => {
      // Poll more frequently for active campaigns
      const status = query?.data?.data?.status;
      if (status === 'sending' || status === 'scheduled') {
        return 5 * 1000; // 5 seconds
      }
      return 30 * 1000; // 30 seconds
    },
    staleTime: 0, // Always consider stale for real-time updates
  });
}