import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { trainingApi } from '../../api';
import { queryKeys, cacheUtils } from '../client';
import { toast } from '../../toast';
import type {
  TrainingCourse,
  TrainingEnrollment,
  Skill,
  SkillLevel,
  Assessment,
  AssessmentResponse,
  SkillRating,
  SkillGap,
  TrainingRecommendation,
  LearningPath,
  CreateTrainingCourseDto,
  UpdateTrainingCourseDto,
  TrainingFiltersDto,
  CreateEnrollmentDto,
  UpdateEnrollmentDto,
  TrainingMetrics,
  CourseAnalytics,
  UserProgress,
} from '../../api';

// Training Course Hooks
export function useTrainingCourses(filters?: TrainingFiltersDto) {
  return useQuery({
    queryKey: queryKeys.training.courses.list(filters),
    queryFn: () => trainingApi.getCourses(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes for course lists
  });
}

export function useInfiniteTrainingCourses(filters?: TrainingFiltersDto) {
  return useInfiniteQuery({
    queryKey: queryKeys.training.courses.list(filters),
    queryFn: ({ pageParam = 1 }) => 
      trainingApi.getCourses({ ...filters, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const currentPage = allPages.length;
      const totalPages = lastPage.pagination?.totalPages || 1;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    staleTime: 5 * 60 * 1000,
  });
}

export function useTrainingCourse(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.training.courses.detail(id),
    queryFn: () => trainingApi.getCourse(id),
    enabled: enabled && !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes for course details
  });
}

export function useTrainingCourseAnalytics(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.training.courses.analytics(id),
    queryFn: () => trainingApi.getCourseAnalytics(id),
    enabled: enabled && !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes for analytics
  });
}

export function useCreateTrainingCourse() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateTrainingCourseDto) => trainingApi.createCourse(data),
    onSuccess: (response) => {
      // Invalidate course lists
      cacheUtils.invalidateQueries(queryKeys.training.courses.lists());
      
      // Add new course to cache
      queryClient.setQueryData(
        queryKeys.training.courses.detail(response.id),
        response
      );
      
      toast.success('Training course created successfully');
    },
  });
}

export function useUpdateTrainingCourse() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTrainingCourseDto }) => 
      trainingApi.updateCourse(id, data),
    onSuccess: (response, variables) => {
      // Update course in cache
      queryClient.setQueryData(
        queryKeys.training.courses.detail(variables.id),
        response
      );
      
      // Invalidate course lists
      cacheUtils.invalidateQueries(queryKeys.training.courses.lists());
      
      toast.success('Training course updated successfully');
    },
  });
}

export function useDeleteTrainingCourse() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => trainingApi.deleteCourse(id),
    onSuccess: (_, id) => {
      // Remove course from cache
      queryClient.removeQueries({ queryKey: queryKeys.training.courses.detail(id) });
      
      // Invalidate course lists
      cacheUtils.invalidateQueries(queryKeys.training.courses.lists());
      
      toast.success('Training course deleted successfully');
    },
  });
}

export function usePublishTrainingCourse() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => trainingApi.publishCourse(id),
    onSuccess: (response, id) => {
      // Update course in cache
      queryClient.setQueryData(
        queryKeys.training.courses.detail(id),
        response
      );
      
      // Invalidate course lists
      cacheUtils.invalidateQueries(queryKeys.training.courses.lists());
      
      toast.success('Training course published successfully');
    },
  });
}

export function useUnpublishTrainingCourse() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => trainingApi.unpublishCourse(id),
    onSuccess: (response, id) => {
      // Update course in cache
      queryClient.setQueryData(
        queryKeys.training.courses.detail(id),
        response
      );
      
      // Invalidate course lists
      cacheUtils.invalidateQueries(queryKeys.training.courses.lists());
      
      toast.success('Training course unpublished successfully');
    },
  });
}

// Training Enrollment Hooks
export function useTrainingEnrollments(userId?: string, courseId?: string) {
  return useQuery({
    queryKey: queryKeys.training.enrollments.list({ userId, courseId }),
    queryFn: () => trainingApi.getEnrollments(userId, courseId),
    staleTime: 2 * 60 * 1000, // 2 minutes for enrollments
  });
}

export function useTrainingEnrollment(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.training.enrollments.detail(id),
    queryFn: () => trainingApi.getEnrollment(id),
    enabled: enabled && !!id,
    staleTime: 2 * 60 * 1000,
  });
}

export function useUserTrainingProgress(userId?: string) {
  return useQuery({
    queryKey: queryKeys.training.enrollments.progress(userId || 'current'),
    queryFn: () => trainingApi.getUserProgress(userId),
    staleTime: 1 * 60 * 1000, // 1 minute for progress
  });
}

export function useCreateTrainingEnrollment() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateEnrollmentDto) => trainingApi.createEnrollment(data),
    onSuccess: (response) => {
      // Invalidate enrollment lists
      cacheUtils.invalidateQueries(queryKeys.training.enrollments.lists());
      
      // Add new enrollment to cache
      queryClient.setQueryData(
        queryKeys.training.enrollments.detail(response.id),
        response
      );
      
      // Invalidate user progress
      cacheUtils.invalidateQueries(queryKeys.training.enrollments.progress('current'));
      
      toast.success('Enrolled in training course successfully');
    },
  });
}

export function useUpdateTrainingEnrollment() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEnrollmentDto }) => 
      trainingApi.updateEnrollment(id, data),
    onSuccess: (response, variables) => {
      // Update enrollment in cache
      queryClient.setQueryData(
        queryKeys.training.enrollments.detail(variables.id),
        response
      );
      
      // Invalidate enrollment lists
      cacheUtils.invalidateQueries(queryKeys.training.enrollments.lists());
      
      // Invalidate user progress
      cacheUtils.invalidateQueries(queryKeys.training.enrollments.progress('current'));
      
      toast.success('Training enrollment updated successfully');
    },
  });
}

export function useUnenrollFromTraining() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => trainingApi.unenroll(id),
    onSuccess: (_, id) => {
      // Remove enrollment from cache
      queryClient.removeQueries({ queryKey: queryKeys.training.enrollments.detail(id) });
      
      // Invalidate enrollment lists
      cacheUtils.invalidateQueries(queryKeys.training.enrollments.lists());
      
      // Invalidate user progress
      cacheUtils.invalidateQueries(queryKeys.training.enrollments.progress('current'));
      
      toast.success('Unenrolled from training course successfully');
    },
  });
}

export function useCompleteTrainingModule() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ enrollmentId, moduleId }: { enrollmentId: string; moduleId: string }) => 
      trainingApi.completeModule(enrollmentId, moduleId),
    onSuccess: (response, variables) => {
      // Update enrollment in cache
      queryClient.setQueryData(
        queryKeys.training.enrollments.detail(variables.enrollmentId),
        response
      );
      
      // Invalidate user progress
      cacheUtils.invalidateQueries(queryKeys.training.enrollments.progress('current'));
      
      toast.success('Module completed successfully');
    },
  });
}

// Skills Management Hooks
export function useSkillsQuery(category?: string) {
  return useQuery({
    queryKey: queryKeys.training.skills.list(category),
    queryFn: () => trainingApi.getSkills(category),
    staleTime: 10 * 60 * 1000, // 10 minutes for skills
  });
}

export function useSkillGapsQuery(userId?: string) {
  return useQuery({
    queryKey: queryKeys.training.skills.gaps(userId),
    queryFn: () => trainingApi.getSkillGaps(userId),
    staleTime: 5 * 60 * 1000, // 5 minutes for skill gaps
  });
}

export function useSkillRatings(userId?: string) {
  return useQuery({
    queryKey: queryKeys.training.skills.ratings(userId),
    queryFn: () => trainingApi.getSkillRatings(userId),
    staleTime: 5 * 60 * 1000,
  });
}

export function useTrainingRecommendations(userId?: string) {
  return useQuery({
    queryKey: queryKeys.training.skills.recommendations(userId),
    queryFn: () => trainingApi.getTrainingRecommendations(userId),
    staleTime: 5 * 60 * 1000,
  });
}

export function useRateSkill() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: { skillId: string; rating: number; userId?: string }) => 
      trainingApi.rateSkill(data.skillId, data.rating, data.userId),
    onSuccess: (_, variables) => {
      // Invalidate skill ratings
      cacheUtils.invalidateQueries(queryKeys.training.skills.ratings(variables.userId));
      
      // Invalidate skill gaps
      cacheUtils.invalidateQueries(queryKeys.training.skills.gaps(variables.userId));
      
      // Invalidate recommendations
      cacheUtils.invalidateQueries(queryKeys.training.skills.recommendations(variables.userId));
      
      toast.success('Skill rated successfully');
    },
  });
}

export function useUpdateSkillLevel() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: { skillId: string; level: SkillLevel; userId?: string }) => 
      trainingApi.updateSkillLevel(data.skillId, data.level, data.userId),
    onSuccess: (_, variables) => {
      // Invalidate skill ratings
      cacheUtils.invalidateQueries(queryKeys.training.skills.ratings(variables.userId));
      
      // Invalidate skill gaps
      cacheUtils.invalidateQueries(queryKeys.training.skills.gaps(variables.userId));
      
      toast.success('Skill level updated successfully');
    },
  });
}

// Assessment Hooks
export function useAssessmentsQuery(filters?: TrainingFiltersDto) {
  return useQuery({
    queryKey: queryKeys.training.assessments.list(filters),
    queryFn: () => trainingApi.getAssessments(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes for assessments
  });
}

export function useAssessment(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.training.assessments.detail(id),
    queryFn: () => trainingApi.getAssessment(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useAssessmentResponses(assessmentId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.training.assessments.responses(assessmentId),
    queryFn: () => trainingApi.getAssessmentResponses(assessmentId),
    enabled: enabled && !!assessmentId,
    staleTime: 2 * 60 * 1000,
  });
}

export function useSubmitAssessment() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: { assessmentId: string; responses: AssessmentResponse[] }) => 
      trainingApi.submitAssessment(data.assessmentId, data.responses),
    onSuccess: (_, variables) => {
      // Invalidate assessment responses
      cacheUtils.invalidateQueries(queryKeys.training.assessments.responses(variables.assessmentId));
      
      // Invalidate user progress
      cacheUtils.invalidateQueries(queryKeys.training.enrollments.progress('current'));
      
      toast.success('Assessment submitted successfully');
    },
  });
}

export function useGradeAssessment() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: { responseId: string; score: number; feedback?: string }) => 
      trainingApi.gradeAssessment(data.responseId, { score: data.score, feedback: data.feedback }),
    onSuccess: (_, variables) => {
      // Invalidate assessment responses - need to determine assessment ID from response
      toast.success('Assessment graded successfully');
    },
  });
}

// Analytics Hooks
export function useTrainingAnalytics(filters?: any) {
  return useQuery({
    queryKey: queryKeys.training.analytics.dashboard(filters),
    queryFn: () => trainingApi.getAnalytics(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes for analytics
  });
}

export function useTrainingMetricsQuery(filters?: any) {
  return useQuery({
    queryKey: queryKeys.training.analytics.metrics(filters),
    queryFn: () => trainingApi.getMetrics(filters),
    staleTime: 5 * 60 * 1000,
  });
}

export function useTrainingReports(filters?: any) {
  return useQuery({
    queryKey: queryKeys.training.analytics.reports(filters),
    queryFn: () => trainingApi.getReports(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes for reports
  });
}

// Learning Path Hooks
export function useLearningPathsQuery(filters?: any) {
  return useQuery({
    queryKey: [...queryKeys.training.all(), 'learning-paths', filters] as const,
    queryFn: () => trainingApi.getLearningPaths(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes for learning paths
  });
}

export function useCreateLearningPath() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: any) => trainingApi.createLearningPath(data),
    onSuccess: () => {
      // Invalidate learning paths
      cacheUtils.invalidateQueries([...queryKeys.training.all(), 'learning-paths']);
      
      toast.success('Learning path created successfully');
    },
  });
}

export function useFollowLearningPath() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (pathId: string) => trainingApi.followLearningPath(pathId),
    onSuccess: () => {
      // Invalidate user progress
      cacheUtils.invalidateQueries(queryKeys.training.enrollments.progress('current'));
      
      // Invalidate enrollments
      cacheUtils.invalidateQueries(queryKeys.training.enrollments.lists());
      
      toast.success('Learning path followed successfully');
    },
  });
}

// Utility hooks for optimistic updates
export function useOptimisticEnrollmentUpdate() {
  const queryClient = useQueryClient();
  
  return (enrollmentId: string, updateFn: (enrollment: TrainingEnrollment) => TrainingEnrollment) => {
    const previousEnrollment = queryClient.getQueryData<{ data: TrainingEnrollment }>(
      queryKeys.training.enrollments.detail(enrollmentId)
    );
    
    if (previousEnrollment) {
      queryClient.setQueryData(
        queryKeys.training.enrollments.detail(enrollmentId),
        {
          ...previousEnrollment,
          data: updateFn(previousEnrollment.data),
        }
      );
    }
    
    return previousEnrollment;
  };
}

export function useOptimisticProgressUpdate() {
  const queryClient = useQueryClient();
  
  return (moduleId: string, completed: boolean) => {
    const previousProgress = queryClient.getQueryData<{ data: UserProgress }>(
      queryKeys.training.enrollments.progress('current')
    );
    
    if (previousProgress) {
      queryClient.setQueryData(
        queryKeys.training.enrollments.progress('current'),
        {
          ...previousProgress,
          data: {
            ...previousProgress.data,
            // Update completion status based on module completion  
            completedCourses: completed 
              ? previousProgress.data.completedCourses + 1 
              : Math.max(0, previousProgress.data.completedCourses - 1),
          },
        }
      );
    }
    
    return previousProgress;
  };
}

// Real-time hooks for progress tracking
export function useTrainingProgressPolling(enrollmentId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.training.enrollments.detail(enrollmentId),
    queryFn: () => trainingApi.getEnrollment(enrollmentId),
    enabled: enabled && !!enrollmentId,
    refetchInterval: (query) => {
      // Poll more frequently for active enrollments
      const status = query?.data?.status;
      if (status === 'in_progress') {
        return 30 * 1000; // 30 seconds
      }
      return 2 * 60 * 1000; // 2 minutes
    },
    staleTime: 0, // Always consider stale for real-time updates
  });
}