/**
 * Routing hooks for TanStack Router
 */

import { useRouter, useRouterState, useNavigate, useLocation, useParams, useSearch } from '@tanstack/react-router'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { NavigationOptions, BreadcrumbItem, RouteTransition, LoadingStates } from './types'
import { generateBreadcrumbs, buildRoute, isRouteActive, parseSearchParams } from './utils'

/**
 * Enhanced navigation hook with analytics and guards
 */
export function useNavigation() {
  const navigate = useNavigate()
  const location = useLocation()
  const router = useRouter()
  
  const navigateWithOptions = useCallback(
    (to: string, options: NavigationOptions = {}) => {
      const {
        replace = false,
        resetScroll = true,
        state,
        search,
        params,
        hash,
        from,
        mask
      } = options
      
      // Track navigation
      console.log(`Navigating from ${location.pathname} to ${to}`)
      
      // Build the complete path
      const fullPath = buildRoute(to, params, search, hash)
      
      // Perform navigation
      navigate({
        to: fullPath,
        replace,
        resetScroll,
        state,
        from,
        mask
      })
    },
    [navigate, location]
  )
  
  const goBack = useCallback(() => {
    window.history.back()
  }, [])
  
  const goForward = useCallback(() => {
    window.history.forward()
  }, [])
  
  const refresh = useCallback(() => {
    window.location.reload()
  }, [])
  
  return {
    navigate: navigateWithOptions,
    goBack,
    goForward,
    refresh,
    location,
    router
  }
}

/**
 * Hook for managing route parameters
 */
export function useRouteParams<T extends Record<string, string> = Record<string, string>>() {
  const params = useParams({ from: '__root__' as any }) // TODO: Fix with proper route typing
  const location = useLocation()
  const navigate = useNavigate()
  
  const updateParams = useCallback(
    (newParams: Partial<T>) => {
      const currentParams = { ...params }
      Object.assign(currentParams, newParams)
      
      // Rebuild the path with new params
      const newPath = buildRoute(location.pathname, currentParams as Record<string, string>)
      
      navigate({
        to: newPath,
        replace: true
      })
    },
    [params, location, navigate]
  )
  
  return {
    params: params as T,
    updateParams
  }
}

/**
 * Hook for managing search parameters
 */
export function useSearchParams<T extends Record<string, any> = Record<string, any>>() {
  const search = useSearch({ from: '__root__' as any }) // TODO: Fix with proper route typing
  const location = useLocation()
  const navigate = useNavigate()
  
  const updateSearch = useCallback(
    (newSearch: Partial<T>) => {
      const currentSearch = { ...search }
      Object.assign(currentSearch, newSearch)
      
      navigate({
        search: currentSearch,
        replace: true
      })
    },
    [search, navigate]
  )
  
  const clearSearch = useCallback(() => {
    navigate({
      search: {} as any, // TODO: Fix search parameter typing for current TanStack Router
      replace: true
    })
  }, [navigate])
  
  return {
    search: search as T,
    updateSearch,
    clearSearch
  }
}

/**
 * Hook for breadcrumb navigation
 */
export function useBreadcrumbs(routeMetadata?: Record<string, any>) {
  const location = useLocation()
  
  const breadcrumbs = useMemo(
    () => generateBreadcrumbs(location.pathname, routeMetadata),
    [location.pathname, routeMetadata]
  )
  
  return breadcrumbs
}

/**
 * Hook for route matching
 */
export function useRouteMatch(pattern: string, exact: boolean = false) {
  const location = useLocation()
  
  const isMatch = useMemo(
    () => isRouteActive(location.pathname, pattern, exact),
    [location.pathname, pattern, exact]
  )
  
  return isMatch
}

/**
 * Hook for route loading states
 */
export function useRouteLoading(): LoadingStates {
  const routerState = useRouterState()
  
  return {
    initial: routerState.isLoading,
    navigation: routerState.isTransitioning,
    preload: (routerState as any).isPending, // TODO: Check current TanStack Router API
    error: (routerState as any).error // TODO: Check current TanStack Router API
  }
}

/**
 * Hook for route transitions
 */
export function useRouteTransition() {
  const location = useLocation()
  const [transition, setTransition] = useState<RouteTransition | null>(null)
  
  useEffect(() => {
    const handleRouteChange = () => {
      const newTransition: RouteTransition = {
        from: transition?.to || '/',
        to: location.pathname,
        type: 'push', // This would need to be determined based on navigation method
        timestamp: Date.now()
      }
      
      setTransition(newTransition)
    }
    
    handleRouteChange()
  }, [location.pathname])
  
  return transition
}

/**
 * Hook for route metadata
 */
export function useRouteMetadata() {
  const location = useLocation()
  const routerState = useRouterState()
  
  // This would typically come from route configuration
  const metadata = useMemo(() => {
    // Extract metadata from current route
    return {
      title: document.title,
      path: location.pathname,
      params: (routerState.location as any).params || {}, // TODO: Check current TanStack Router API
      search: routerState.location.search
    }
  }, [location, routerState])
  
  return metadata
}

/**
 * Hook for programmatic route validation
 */
export function useRouteValidation() {
  const location = useLocation()
  const navigate = useNavigate()
  
  const validateAndNavigate = useCallback(
    (to: string, options: NavigationOptions = {}) => {
      // Perform validation logic here
      const isValid = true // Replace with actual validation
      
      if (!isValid) {
        console.warn(`Invalid route: ${to}`)
        return false
      }
      
      navigate({
        to,
        ...options
      })
      
      return true
    },
    [navigate]
  )
  
  return {
    validateAndNavigate,
    currentPath: location.pathname,
    isValid: true // This would be computed based on current route
  }
}

/**
 * Hook for route caching
 */
export function useRouteCache() {
  const location = useLocation()
  const [cache, setCache] = useState<Map<string, any>>(new Map())
  
  const getCached = useCallback(
    (key: string) => {
      return cache.get(key)
    },
    [cache]
  )
  
  const setCached = useCallback(
    (key: string, value: any) => {
      setCache(prev => new Map(prev).set(key, value))
    },
    []
  )
  
  const clearCache = useCallback(() => {
    setCache(new Map())
  }, [])
  
  const clearCached = useCallback(
    (key: string) => {
      setCache(prev => {
        const newCache = new Map(prev)
        newCache.delete(key)
        return newCache
      })
    },
    []
  )
  
  return {
    getCached,
    setCached,
    clearCache,
    clearCached,
    currentPath: location.pathname
  }
}

/**
 * Hook for route analytics
 */
export function useRouteAnalytics() {
  const location = useLocation()
  const transition = useRouteTransition()
  
  const trackPageView = useCallback(
    (customPath?: string, customTitle?: string) => {
      const path = customPath || location.pathname
      const title = customTitle || document.title
      
      // Track page view
      console.log(`Page view: ${path}`, { title })
      
      // Send to analytics service
      if (typeof window !== 'undefined' && (window as any).__analyticsStore) {
        (window as any).__analyticsStore.trackPageView(path, title)
      }
    },
    [location.pathname]
  )
  
  const trackEvent = useCallback(
    (event: string, properties?: Record<string, any>) => {
      console.log(`Event: ${event}`, properties)
      
      // Send to analytics service
      if (typeof window !== 'undefined' && (window as any).__analyticsStore) {
        (window as any).__analyticsStore.trackEvent(event, {
          ...properties,
          currentPath: location.pathname
        })
      }
    },
    [location.pathname]
  )
  
  return {
    trackPageView,
    trackEvent,
    currentPath: location.pathname,
    transition
  }
}

/**
 * Hook for route prefetching
 */
export function useRoutePrefetch() {
  const router = useRouter()
  
  const prefetch = useCallback(
    (to: string) => {
      router.preloadRoute({ to })
    },
    [router]
  )
  
  const prefetchOnHover = useCallback(
    (to: string) => {
      return {
        onMouseEnter: () => prefetch(to),
        onFocus: () => prefetch(to)
      }
    },
    [prefetch]
  )
  
  return {
    prefetch,
    prefetchOnHover
  }
}

/**
 * Hook for route history
 */
export function useRouteHistory() {
  const [history, setHistory] = useState<string[]>([])
  const location = useLocation()
  
  useEffect(() => {
    setHistory(prev => [...prev, location.pathname].slice(-10)) // Keep last 10 routes
  }, [location.pathname])
  
  const canGoBack = history.length > 1
  const previousRoute = history[history.length - 2]
  
  return {
    history,
    canGoBack,
    previousRoute,
    currentRoute: location.pathname
  }
}

/**
 * Hook for route permissions
 */
export function useRoutePermissions() {
  const location = useLocation()
  // This would typically come from auth context
  const userPermissions = ['read', 'write'] // Mock permissions
  
  const hasPermission = useCallback(
    (permission: string) => {
      return userPermissions.includes(permission)
    },
    [userPermissions]
  )
  
  const hasAnyPermission = useCallback(
    (permissions: string[]) => {
      return permissions.some(permission => userPermissions.includes(permission))
    },
    [userPermissions]
  )
  
  const hasAllPermissions = useCallback(
    (permissions: string[]) => {
      return permissions.every(permission => userPermissions.includes(permission))
    },
    [userPermissions]
  )
  
  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    userPermissions,
    currentPath: location.pathname
  }
}

/**
 * Hook for route title management
 */
export function useRouteTitle(title?: string) {
  const location = useLocation()
  
  useEffect(() => {
    if (title) {
      document.title = `${title} - Luminar`
    } else {
      // Generate title from path
      const segments = location.pathname.split('/').filter(Boolean)
      const pageTitle = segments.length > 0 
        ? segments[segments.length - 1].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        : 'Home'
      
      document.title = `${pageTitle} - Luminar`
    }
  }, [title, location.pathname])
  
  return {
    setTitle: (newTitle: string) => {
      document.title = `${newTitle} - Luminar`
    },
    currentTitle: document.title
  }
}

/**
 * Hook for route keyboard navigation
 */
export function useRouteKeyboard() {
  const { navigate, goBack } = useNavigation()
  
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + Left Arrow: Go back
      if (event.altKey && event.key === 'ArrowLeft') {
        event.preventDefault()
        goBack()
      }
      
      // Alt + Right Arrow: Go forward
      if (event.altKey && event.key === 'ArrowRight') {
        event.preventDefault()
        window.history.forward()
      }
      
      // Ctrl/Cmd + K: Open command palette (if available)
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault()
        // This would open a command palette for navigation
        console.log('Command palette shortcut pressed')
      }
    }
    
    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [navigate, goBack])
  
  return {
    registerShortcut: (key: string, handler: () => void) => {
      // Register custom keyboard shortcuts
      console.log(`Registered shortcut: ${key}`)
    }
  }
}